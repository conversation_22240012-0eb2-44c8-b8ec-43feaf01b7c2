# Dynamic Entity Builder

A powerful Angular library for creating dynamic entity forms with drag-and-drop field builder functionality. Features a beautiful glassmorphism design with smooth slide animations.

## Features

-   🎨 **Glassmorphism Design**: Modern, translucent UI with backdrop blur effects
-   🎯 **Drag & Drop**: Intuitive field type selection with drag-and-drop functionality
-   📱 **Responsive Layout**: Works seamlessly across different screen sizes
-   🔧 **Dynamic Sections**: Create multiple sections with customizable labels
-   📊 **Grid System**: Flexible row and column layout system
-   ✨ **Smooth Animations**: Slide transitions between views
-   🎭 **Field Types**: Support for text, number, email, date, select, textarea, checkbox, file, and color fields

## Installation

```bash
npm install dynamic-entity
```

## Dependencies

Make sure you have the following peer dependencies installed:

```bash
npm install @angular/cdk @angular/forms
```

## Usage

### Basic Implementation

1. Import the module in your app:

```typescript
import { DynamicEntityModule } from 'dynamic-entity';

@NgModule({
    imports: [DynamicEntityModule],
})
export class AppModule {}
```

2. Use the floating widget component:

```html
<lib-dynamic-entity-float-widget
    (entitySelected)="onEntitySelected($event)"
    (fieldsBuilt)="onFieldsBuilt($event)"
>
</lib-dynamic-entity-float-widget>
```

3. Handle the events in your component:

```typescript
import { Entity, DynamicSection } from 'dynamic-entity';

export class AppComponent {
    onEntitySelected(entity: Entity): void {
        console.log('Selected entity:', entity);
    }

    onFieldsBuilt(sections: DynamicSection[]): void {
        console.log('Built field structure:', sections);
    }
}
```

### Custom Entity List

You can provide your own entity list:

```typescript
import { Entity } from 'dynamic-entity';

const customEntities: Entity[] = [
    {
        id: 'custom-1',
        name: 'Custom Form',
        customFieldsCount: 5,
        description: 'A custom form for specific needs',
    },
];
```

```html
<lib-dynamic-entity-popup
    [isVisible]="showPopup"
    [entities]="customEntities"
    (close)="showPopup = false"
    (entitySelected)="onEntitySelected($event)"
    (fieldsBuilt)="onFieldsBuilt($event)"
>
</lib-dynamic-entity-popup>
```

### Demo Component

For a quick start, use the demo component:

```html
<lib-dynamic-entity-demo></lib-dynamic-entity-demo>
```

## API Reference

### Components

#### DynamicEntityPopupComponent

**Inputs:**

-   `isVisible: boolean` - Controls popup visibility
-   `entities: Entity[]` - List of available entities

**Outputs:**

-   `close: EventEmitter<void>` - Emitted when popup is closed
-   `entitySelected: EventEmitter<Entity>` - Emitted when an entity is selected
-   `fieldsBuilt: EventEmitter<DynamicSection[]>` - Emitted when field structure is built

#### DynamicEntityFloatWidgetComponent

A complete widget that includes both the floating button and popup.

**Outputs:**

-   `entitySelected: EventEmitter<Entity>` - Emitted when an entity is selected
-   `fieldsBuilt: EventEmitter<DynamicSection[]>` - Emitted when field structure is built

### Interfaces

#### Entity

```typescript
interface Entity {
    id: string;
    name: string;
    customFieldsCount: number;
    description?: string;
}
```

#### DynamicSection

```typescript
interface DynamicSection {
    id: string;
    label: string;
    rows: DynamicRow[];
    isEditing?: boolean;
}
```

#### DynamicRow

```typescript
interface DynamicRow {
    id: string;
    columns: DynamicColumn[];
}
```

#### DynamicColumn

```typescript
interface DynamicColumn {
    id: string;
    field?: DynamicField;
    width: number; // 1-12 for grid system
}
```

#### DynamicField

```typescript
interface DynamicField {
    id: string;
    fieldType: FieldType;
    label: string;
    required: boolean;
    placeholder?: string;
    options?: string[];
}
```

## Styling

The library uses CSS custom properties for easy theming. You can override the default colors:

```css
:root {
    --dynamic-entity-primary: #667eea;
    --dynamic-entity-secondary: #764ba2;
    --dynamic-entity-background: rgba(255, 255, 255, 0.95);
    --dynamic-entity-border: rgba(255, 255, 255, 0.3);
}
```

## Development

### Build

```bash
ng build dynamic-entity
```

### Test

```bash
ng test dynamic-entity
```

### Serve Demo

```bash
ng serve
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.
