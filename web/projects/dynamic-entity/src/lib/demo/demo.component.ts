import { Component } from '@angular/core';
import { Entity, DynamicSection } from '../interfaces/dynamic-entity.interfaces';

@Component({
    selector: 'lib-dynamic-entity-demo',
    template: `
        <div class="demo-container">
            <h1>Dynamic Entity Builder Demo</h1>
            <p>Click the floating button to open the dynamic entity builder</p>
            
            <lib-dynamic-entity-float-widget
                (entitySelected)="onEntitySelected($event)"
                (fieldsBuilt)="onFieldsBuilt($event)">
            </lib-dynamic-entity-float-widget>
            
            <div class="demo-output" *ngIf="selectedEntity">
                <h2>Selected Entity: {{ selectedEntity.name }}</h2>
                <p>{{ selectedEntity.description }}</p>
            </div>
            
            <div class="demo-output" *ngIf="builtFields.length > 0">
                <h2>Built Fields Structure:</h2>
                <pre>{{ builtFields | json }}</pre>
            </div>
        </div>
    `,
    styles: [`
        .demo-container {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        h1 {
            color: #333;
            margin-bottom: 16px;
        }
        
        p {
            color: #666;
            margin-bottom: 32px;
        }
        
        .demo-output {
            margin-top: 32px;
            padding: 24px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .demo-output h2 {
            margin: 0 0 16px 0;
            color: #333;
            font-size: 18px;
        }
        
        pre {
            background: #fff;
            padding: 16px;
            border-radius: 4px;
            border: 1px solid #ddd;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
    `]
})
export class DynamicEntityDemoComponent {
    selectedEntity: Entity | null = null;
    builtFields: DynamicSection[] = [];

    onEntitySelected(entity: Entity): void {
        this.selectedEntity = entity;
        console.log('Entity selected:', entity);
    }

    onFieldsBuilt(fields: DynamicSection[]): void {
        this.builtFields = fields;
        console.log('Fields built:', fields);
    }
}
