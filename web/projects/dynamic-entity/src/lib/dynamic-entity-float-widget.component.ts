import { Component } from '@angular/core';

@Component({
  selector: 'lib-dynamic-entity-float-widget',
  template: `
    <lib-dynamic-entity-float-button 
      (buttonClick)="onFloatButtonClick()">
    </lib-dynamic-entity-float-button>
    
    <lib-dynamic-entity-popup 
      [isVisible]="isPopupVisible"
      (close)="onPopupClose()"
      (confirm)="onPopupConfirm()">
    </lib-dynamic-entity-popup>
  `,
  styles: []
})
export class DynamicEntityFloatWidgetComponent {
  isPopupVisible: boolean = false;

  onFloatButtonClick(): void {
    this.isPopupVisible = true;
  }

  onPopupClose(): void {
    this.isPopupVisible = false;
  }

  onPopupConfirm(): void {
    // Handle confirm action here
    console.log('Popup confirmed');
    this.isPopupVisible = false;
  }
}
