<div class="popup-overlay" *ngIf="isVisible" (click)="onOverlayClick()">
    <div class="popup-container" (click)="$event.stopPropagation()">
        <!-- Header -->
        <div class="popup-header">
            <div class="header-content">
                <button
                    class="back-button"
                    *ngIf="currentView === 'builder'"
                    (click)="goBackToEntities()"
                >
                    <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M19 12H5"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                        <path
                            d="M12 19L5 12L12 5"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                    </svg>
                </button>
                <h2 class="popup-title">
                    {{
                        currentView === 'entities'
                            ? 'Dynamic Entities'
                            : selectedEntity?.name + ' - Field Builder'
                    }}
                </h2>
            </div>
            <button class="close-button" (click)="onClose()">
                <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M18 6L6 18"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    />
                    <path
                        d="M6 6L18 18"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    />
                </svg>
            </button>
        </div>

        <!-- Content Container with Slide Animation -->
        <div class="content-container" [class.slide-left]="isSliding">
            <!-- Entities View -->
            <div class="view entities-view" *ngIf="currentView === 'entities'">
                <div class="entities-grid">
                    <div
                        class="entity-card"
                        *ngFor="let entity of entities"
                        (click)="selectEntity(entity)"
                    >
                        <div class="entity-icon">
                            <svg
                                width="32"
                                height="32"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M20 6L9 17L4 12"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                        </div>
                        <div class="entity-info">
                            <h3 class="entity-name">
                                {{ entity.name }}
                            </h3>
                            <p class="entity-fields-count">
                                {{ entity.customFieldsCount }} custom fields
                            </p>
                            <p
                                class="entity-description"
                                *ngIf="entity.description"
                            >
                                {{ entity.description }}
                            </p>
                        </div>
                        <div class="entity-arrow">
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M9 18L15 12L9 6"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Field Builder View -->
            <div class="view builder-view" *ngIf="currentView === 'builder'">
                <!-- Field Types Toolbar -->
                <div
                    class="field-types-toolbar"
                    cdkDropList
                    [cdkDropListData]="fieldTypes"
                    cdkDropListSortingDisabled
                >
                    <div
                        class="field-type-item"
                        *ngFor="let fieldType of fieldTypes"
                        cdkDrag
                        [cdkDragData]="fieldType"
                    >
                        <div
                            class="field-type-icon"
                            [innerHTML]="fieldType.icon"
                        ></div>
                        <span class="field-type-name">{{
                            fieldType.name
                        }}</span>
                    </div>
                </div>

                <!-- Dynamic Sections -->
                <div class="sections-container">
                    <div
                        class="section"
                        *ngFor="
                            let section of sections;
                            let sectionIndex = index
                        "
                    >
                        <!-- Section Header -->
                        <div class="section-header">
                            <div
                                class="section-title"
                                *ngIf="!section.isEditing"
                                (click)="editSectionLabel(section)"
                            >
                                {{ section.label }}
                                <svg
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                    <path
                                        d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                </svg>
                            </div>
                            <input
                                class="section-title-input"
                                *ngIf="section.isEditing"
                                [(ngModel)]="section.label"
                                (blur)="saveSectionLabel(section)"
                                (keyup.enter)="saveSectionLabel(section)"
                                #sectionInput
                            />
                            <div class="section-actions">
                                <button
                                    class="section-action-btn"
                                    (click)="addRowToSection(section)"
                                >
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M12 5v14"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        />
                                        <path
                                            d="M5 12h14"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        />
                                    </svg>
                                </button>
                                <button
                                    class="section-action-btn delete"
                                    (click)="removeSection(sectionIndex)"
                                >
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M3 6h18"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        />
                                        <path
                                            d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Section Rows -->
                        <div class="section-rows">
                            <div
                                class="row"
                                *ngFor="
                                    let row of section.rows;
                                    let rowIndex = index
                                "
                            >
                                <div class="row-header">
                                    <span class="row-label"
                                        >Row {{ rowIndex + 1 }}</span
                                    >
                                    <div class="row-actions">
                                        <button
                                            class="row-action-btn"
                                            (click)="addColumnToRow(row)"
                                        >
                                            <svg
                                                width="14"
                                                height="14"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M12 5v14"
                                                    stroke="currentColor"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                                <path
                                                    d="M5 12h14"
                                                    stroke="currentColor"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                            </svg>
                                        </button>
                                        <button
                                            class="row-action-btn delete"
                                            (click)="
                                                removeRowFromSection(
                                                    section,
                                                    rowIndex
                                                )
                                            "
                                        >
                                            <svg
                                                width="14"
                                                height="14"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M18 6L6 18"
                                                    stroke="currentColor"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                                <path
                                                    d="M6 6L18 18"
                                                    stroke="currentColor"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Row Columns -->
                                <div class="row-columns">
                                    <div
                                        class="column"
                                        *ngFor="
                                            let column of row.columns;
                                            let colIndex = index
                                        "
                                        [style.flex]="column.width"
                                        cdkDropList
                                        [cdkDropListData]="[column]"
                                        (cdkDropListDropped)="
                                            onFieldDrop($event, column)
                                        "
                                    >
                                        <!-- Empty Column State -->
                                        <div
                                            class="column-placeholder"
                                            *ngIf="!column.field"
                                        >
                                            <div class="placeholder-content">
                                                <svg
                                                    width="24"
                                                    height="24"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M12 5v14"
                                                        stroke="currentColor"
                                                        stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                    />
                                                    <path
                                                        d="M5 12h14"
                                                        stroke="currentColor"
                                                        stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                    />
                                                </svg>
                                                <span>Drop field here</span>
                                            </div>
                                        </div>

                                        <!-- Field in Column -->
                                        <div
                                            class="field-item"
                                            *ngIf="column.field"
                                        >
                                            <div class="field-header">
                                                <div
                                                    class="field-icon"
                                                    [innerHTML]="
                                                        column.field.fieldType
                                                            .icon
                                                    "
                                                ></div>
                                                <span class="field-label">{{
                                                    column.field.label
                                                }}</span>
                                                <button
                                                    class="field-remove-btn"
                                                    (click)="
                                                        removeFieldFromColumn(
                                                            column
                                                        )
                                                    "
                                                >
                                                    <svg
                                                        width="14"
                                                        height="14"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M18 6L6 18"
                                                            stroke="currentColor"
                                                            stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                        <path
                                                            d="M6 6L18 18"
                                                            stroke="currentColor"
                                                            stroke-width="2"
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                        />
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="field-preview">
                                                <input
                                                    type="text"
                                                    [placeholder]="
                                                        column.field
                                                            .placeholder ||
                                                        'Enter ' +
                                                            column.field.label
                                                    "
                                                    readonly
                                                />
                                            </div>
                                        </div>

                                        <!-- Column Actions -->
                                        <div
                                            class="column-actions"
                                            *ngIf="row.columns.length > 1"
                                        >
                                            <button
                                                class="column-action-btn delete"
                                                (click)="
                                                    removeColumnFromRow(
                                                        row,
                                                        colIndex
                                                    )
                                                "
                                            >
                                                <svg
                                                    width="12"
                                                    height="12"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M18 6L6 18"
                                                        stroke="currentColor"
                                                        stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                    />
                                                    <path
                                                        d="M6 6L18 18"
                                                        stroke="currentColor"
                                                        stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                    />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Section Button -->
                    <div class="add-section-container">
                        <button class="add-section-btn" (click)="addSection()">
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M12 5v14"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                                <path
                                    d="M5 12h14"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                            Add Section
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
