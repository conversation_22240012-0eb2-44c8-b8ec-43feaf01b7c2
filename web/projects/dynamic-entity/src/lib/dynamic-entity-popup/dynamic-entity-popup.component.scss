.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease-out;
}

.popup-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.back-button {
    background: rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #666;
}

.back-button:hover {
    background: rgba(0, 0, 0, 0.2);
    color: #333;
}

.popup-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.close-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #666;
}

.close-button:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.content-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.content-container.slide-left {
    animation: slideLeft 0.3s ease-out;
}

.view {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    padding: 24px;
}

/* Entities View Styles */
.entities-view {
    background: rgba(255, 255, 255, 0.1);
}

.entities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.entity-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.entity-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.95);
}

.entity-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.entity-info {
    flex: 1;
}

.entity-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.entity-fields-count {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #667eea;
    font-weight: 500;
}

.entity-description {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.entity-arrow {
    color: #999;
    transition: all 0.2s ease;
}

.entity-card:hover .entity-arrow {
    color: #667eea;
    transform: translateX(4px);
}

/* Field Builder Styles */
.builder-view {
    background: rgba(255, 255, 255, 0.05);
}

.field-types-toolbar {
    display: flex;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    margin-bottom: 24px;
    overflow-x: auto;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.field-type-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    cursor: grab;
    transition: all 0.2s ease;
    min-width: 80px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.field-type-item:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.field-type-item.cdk-drag-dragging {
    cursor: grabbing;
    transform: rotate(5deg);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.field-type-icon {
    width: 24px;
    height: 24px;
    color: #667eea;
}

.field-type-name {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    text-align: center;
}

.sections-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.8);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
}

.section-title:hover {
    color: #667eea;
}

.section-title svg {
    width: 14px;
    height: 14px;
    opacity: 0.6;
}

.section-title-input {
    border: none;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    outline: none;
    border: 2px solid #667eea;
}

.section-actions {
    display: flex;
    gap: 8px;
}

.section-action-btn {
    background: rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    padding: 6px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #666;
}

.section-action-btn:hover {
    background: rgba(0, 0, 0, 0.15);
    color: #333;
}

.section-action-btn.delete:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* Section Rows */
.section-rows {
    padding: 20px;
}

.row {
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.row-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.row-label {
    font-size: 14px;
    font-weight: 500;
    color: #666;
}

.row-actions {
    display: flex;
    gap: 6px;
}

.row-action-btn {
    background: rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #666;

    &:hover {
        background: rgba(0, 0, 0, 0.15);
        color: #333;
    }

    &.delete:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }
}

.row-columns {
    display: flex;
    gap: 12px;
}

.column {
    background: rgba(255, 255, 255, 0.8);
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-height: 120px;
    position: relative;
    transition: all 0.2s ease;

    &.cdk-drop-list-dragging {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }
}

.column-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 120px;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #999;
    font-size: 14px;
    text-align: center;

    svg {
        opacity: 0.5;
    }
}

.field-item {
    padding: 12px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.field-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.field-icon {
    width: 16px;
    height: 16px;
    color: #667eea;
    flex-shrink: 0;
}

.field-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    flex: 1;
}

.field-remove-btn {
    background: rgba(239, 68, 68, 0.1);
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #ef4444;
    opacity: 0;

    &:hover {
        background: rgba(239, 68, 68, 0.2);
        opacity: 1;
    }
}

.field-item:hover .field-remove-btn {
    opacity: 1;
}

.field-preview {
    flex: 1;

    input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        color: #666;
        outline: none;
    }
}

.column-actions {
    position: absolute;
    top: 4px;
    right: 4px;
    opacity: 0;
    transition: all 0.2s ease;
}

.column:hover .column-actions {
    opacity: 1;
}

.column-action-btn {
    background: rgba(239, 68, 68, 0.1);
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #ef4444;

    &:hover {
        background: rgba(239, 68, 68, 0.2);
    }
}

/* Add Section Button */
.add-section-container {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.add-section-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: rgba(102, 126, 234, 0.1);
    border: 2px dashed #667eea;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #667eea;
    font-weight: 500;

    &:hover {
        background: rgba(102, 126, 234, 0.15);
        border-color: #764ba2;
        color: #764ba2;
        transform: translateY(-2px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideLeft {
    from {
        transform: translateX(20px);
        opacity: 0.8;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
