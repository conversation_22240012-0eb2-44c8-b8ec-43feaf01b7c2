import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';

import { DynamicEntityComponent } from './dynamic-entity.component';
import { DynamicEntityFloatButtonComponent } from './dynamic-entity-float-button.component';
import { DynamicEntityPopupComponent } from './dynamic-entity-popup/dynamic-entity-popup.component';
import { DynamicEntityFloatWidgetComponent } from './dynamic-entity-float-widget.component';
import { DynamicEntityDemoComponent } from './demo/demo.component';

@NgModule({
    declarations: [
        DynamicEntityComponent,
        DynamicEntityFloatButtonComponent,
        DynamicEntityPopupComponent,
        DynamicEntityFloatWidgetComponent,
        DynamicEntityDemoComponent,
    ],
    imports: [CommonModule, FormsModule, DragDropModule],
    exports: [
        DynamicEntityComponent,
        DynamicEntityFloatButtonComponent,
        DynamicEntityPopupComponent,
        DynamicEntityFloatWidgetComponent,
        DynamicEntityDemoComponent,
    ],
})
export class DynamicEntityModule {}
